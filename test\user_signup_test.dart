import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:rideoon/views/authentication/userauth/sign_up.dart';

void main() {
  group('User Sign Up Screen Tests', () {
    testWidgets('Sign up screen should be scrollable and responsive', (WidgetTester tester) async {
      // Test normal screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: UserSignUp(),
        ),
      );

      // Verify that SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
      
      // Verify key elements are present
      expect(find.text('Sign up'), findsOneWidget);
      expect(find.text('Create your account'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(5)); // Name, Email, Phone, Password, Confirm Password
    });

    testWidgets('Sign up screen should handle short screens', (WidgetTester tester) async {
      // Test short screen size
      await tester.binding.setSurfaceSize(const Size(400, 400));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: UserSignUp(),
        ),
      );

      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
      
      // Verify that content is still accessible
      expect(find.text('Sign up'), findsOneWidget);
      expect(find.text('Create your account'), findsOneWidget);
    });

    testWidgets('Sign up screen should handle smartwatch size', (WidgetTester tester) async {
      // Test smartwatch size
      await tester.binding.setSurfaceSize(const Size(300, 300));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: UserSignUp(),
        ),
      );

      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
      
      // Verify that content is still accessible
      expect(find.text('Sign up'), findsOneWidget);
    });

    testWidgets('Sign up screen should handle tablet size', (WidgetTester tester) async {
      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(800, 1200));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: UserSignUp(),
        ),
      );

      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
      
      // Verify that content is properly sized for tablet
      expect(find.text('Sign up'), findsOneWidget);
      expect(find.text('Create your account'), findsOneWidget);
    });

    testWidgets('Form validation should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UserSignUp(),
        ),
      );

      // Try to submit empty form
      await tester.tap(find.text('Sign up'));
      await tester.pump();

      // Should show validation errors
      expect(find.text('Please enter your full name'), findsOneWidget);
      expect(find.text('Please enter your email'), findsOneWidget);
    });

    testWidgets('Terms checkbox should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UserSignUp(),
        ),
      );

      // Find and tap the checkbox area
      final checkboxFinder = find.byType(GestureDetector).first;
      await tester.tap(checkboxFinder);
      await tester.pump();

      // Checkbox should be checked (this is a basic test, actual state checking would need more setup)
      expect(tester.takeException(), isNull);
    });
  });
}
