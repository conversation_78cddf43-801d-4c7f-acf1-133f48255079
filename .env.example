# Example Environment Configuration File
# Copy this file to .env and fill in your actual values

# App Configuration
APP_NAME=RideOn
APP_VERSION=1.0.0
APP_ENVIRONMENT=development

# API Configuration
API_BASE_URL=https://api.rideon.com
API_TIMEOUT=30000
API_KEY=your_api_key_here

# Database Configuration
DATABASE_URL=your_database_url_here

# Authentication
JWT_SECRET=your_jwt_secret_here
OAUTH_CLIENT_ID=your_oauth_client_id
OAUTH_CLIENT_SECRET=your_oauth_client_secret

# Third-party Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
FIREBASE_PROJECT_ID=your_firebase_project_id
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# Push Notifications
FCM_SERVER_KEY=your_fcm_server_key

# Feature Flags
ENABLE_DEBUG_MODE=true
ENABLE_ANALYTICS=false
ENABLE_CRASH_REPORTING=true

# Logging
LOG_LEVEL=debug
ENABLE_REMOTE_LOGGING=false

# App Store Configuration
ANDROID_APP_ID=com.example.rideoon
IOS_APP_ID=com.example.rideoon
